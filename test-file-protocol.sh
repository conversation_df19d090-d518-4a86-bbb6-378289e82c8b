#!/bin/bash

echo "========================================"
echo "VAITH File Protocol Compatibility Test"
echo "========================================"
echo ""
echo "This will open the VAITH admin system using file:// protocol"
echo "(no server required - works offline)"
echo ""
echo "Opening test pages in your default browser..."
echo ""

# Get the current directory
CURRENT_DIR=$(pwd)

# Open the file protocol test page
if command -v open >/dev/null 2>&1; then
    # macOS
    open "file://$CURRENT_DIR/file-protocol-test.html"
    sleep 2
    open "file://$CURRENT_DIR/admin-products.html"
elif command -v xdg-open >/dev/null 2>&1; then
    # Linux
    xdg-open "file://$CURRENT_DIR/file-protocol-test.html"
    sleep 2
    xdg-open "file://$CURRENT_DIR/admin-products.html"
else
    echo "Could not detect system browser opener."
    echo "Please manually open these files in your browser:"
    echo "- file://$CURRENT_DIR/file-protocol-test.html"
    echo "- file://$CURRENT_DIR/admin-products.html"
fi

echo ""
echo "Pages opened! You can now:"
echo "1. Test adding products in the admin panel"
echo "2. View products on the frontend pages"
echo "3. Use the file protocol test page for debugging"
echo ""
echo "Press Enter to also open the frontend pages..."
read -r

# Open frontend pages
if command -v open >/dev/null 2>&1; then
    # macOS
    open "file://$CURRENT_DIR/products.html"
    sleep 1
    open "file://$CURRENT_DIR/index.html"
elif command -v xdg-open >/dev/null 2>&1; then
    # Linux
    xdg-open "file://$CURRENT_DIR/products.html"
    sleep 1
    xdg-open "file://$CURRENT_DIR/index.html"
else
    echo "Please manually open these files in your browser:"
    echo "- file://$CURRENT_DIR/products.html"
    echo "- file://$CURRENT_DIR/index.html"
fi

echo ""
echo "All pages opened successfully!"
echo "The system is now running in file:// protocol mode."
echo ""
echo "Press Enter to exit..."
read -r
