<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>File Protocol Test - VAITH Add Product</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/admin.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            background: var(--admin-bg);
            color: var(--admin-text-primary);
            font-family: 'Inter', sans-serif;
            padding: 2rem;
            min-height: 100vh;
        }
        
        .test-container {
            max-width: 1000px;
            margin: 0 auto;
            background: var(--admin-card-bg);
            border-radius: 12px;
            padding: 2rem;
            box-shadow: var(--admin-shadow-lg);
        }
        
        .test-header {
            text-align: center;
            margin-bottom: 2rem;
            padding-bottom: 2rem;
            border-bottom: 1px solid var(--admin-border);
        }
        
        .test-title {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
            background: linear-gradient(135deg, #3b82f6, #8b5cf6);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .protocol-info {
            background: var(--admin-bg);
            border: 1px solid var(--admin-border);
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 2rem;
            font-family: 'Courier New', monospace;
            font-size: 0.875rem;
        }
        
        .test-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
            margin-bottom: 2rem;
        }
        
        .test-section {
            background: var(--admin-bg);
            border: 1px solid var(--admin-border);
            border-radius: 8px;
            padding: 1.5rem;
        }
        
        .section-title {
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .test-actions {
            display: flex;
            flex-direction: column;
            gap: 0.75rem;
        }
        
        .btn {
            padding: 0.75rem 1rem;
            border: none;
            border-radius: 6px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            text-decoration: none;
            text-align: center;
            justify-content: center;
        }
        
        .btn-primary {
            background: var(--admin-primary);
            color: white;
        }
        
        .btn-secondary {
            background: var(--admin-border);
            color: var(--admin-text-primary);
        }
        
        .btn-success {
            background: var(--admin-success);
            color: white;
        }
        
        .btn-danger {
            background: var(--admin-danger);
            color: white;
        }
        
        .status-log {
            background: var(--admin-bg);
            border: 1px solid var(--admin-border);
            border-radius: 8px;
            padding: 1rem;
            max-height: 300px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 0.875rem;
            white-space: pre-wrap;
            margin-top: 1rem;
        }
        
        .quick-form {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
            margin-bottom: 1rem;
        }
        
        .form-group {
            display: flex;
            flex-direction: column;
        }
        
        .form-group.full-width {
            grid-column: 1 / -1;
        }
        
        label {
            font-weight: 600;
            margin-bottom: 0.5rem;
            color: var(--admin-text-primary);
        }
        
        input, select, textarea {
            padding: 0.75rem;
            border: 1px solid var(--admin-border);
            border-radius: 6px;
            background: var(--admin-card-bg);
            color: var(--admin-text-primary);
            font-size: 0.875rem;
        }
        
        .results-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }
        
        .result-card {
            background: var(--admin-card-bg);
            border: 1px solid var(--admin-border);
            border-radius: 8px;
            padding: 1rem;
        }
        
        .result-title {
            font-weight: 600;
            margin-bottom: 0.5rem;
        }
        
        .result-price {
            color: var(--admin-success);
            font-weight: 600;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1 class="test-title">🧪 File Protocol Compatibility Test</h1>
            <p>Testing VAITH add product functionality with direct file access (file:// protocol)</p>
        </div>
        
        <div class="protocol-info" id="protocolInfo">
            <strong>Current Protocol:</strong> <span id="currentProtocol">Loading...</span><br>
            <strong>localStorage Available:</strong> <span id="localStorageStatus">Checking...</span><br>
            <strong>Current Products:</strong> <span id="currentProductCount">Loading...</span>
        </div>
        
        <div class="test-grid">
            <div class="test-section">
                <h3 class="section-title">
                    <i class="fas fa-plus"></i>
                    Quick Add Product
                </h3>
                <form id="quickAddForm" class="quick-form">
                    <div class="form-group">
                        <label for="quickName">Product Name</label>
                        <input type="text" id="quickName" name="name" required placeholder="Test Product" value="File Protocol Test Product">
                    </div>
                    
                    <div class="form-group">
                        <label for="quickCategory">Category</label>
                        <select id="quickCategory" name="category" required>
                            <option value="women">Women</option>
                            <option value="men">Men</option>
                            <option value="accessories">Accessories</option>
                            <option value="shoes">Shoes</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="quickPrice">Price</label>
                        <input type="number" id="quickPrice" name="price" step="0.01" required placeholder="29.99" value="39.99">
                    </div>
                    
                    <div class="form-group">
                        <label for="quickStock">Stock</label>
                        <input type="number" id="quickStock" name="stock" required placeholder="10" value="15">
                    </div>
                    
                    <div class="form-group full-width">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-plus"></i> Add Product
                        </button>
                    </div>
                </form>
            </div>
            
            <div class="test-section">
                <h3 class="section-title">
                    <i class="fas fa-cog"></i>
                    Test Actions
                </h3>
                <div class="test-actions">
                    <button class="btn btn-secondary" onclick="checkSystem()">
                        <i class="fas fa-check"></i> Check System Status
                    </button>
                    <button class="btn btn-success" onclick="listProducts()">
                        <i class="fas fa-list"></i> List All Products
                    </button>
                    <button class="btn btn-danger" onclick="clearAllProducts()">
                        <i class="fas fa-trash"></i> Clear All Products
                    </button>
                    <a href="admin-products.html" class="btn btn-primary">
                        <i class="fas fa-external-link-alt"></i> Open Admin Panel
                    </a>
                    <a href="products.html" class="btn btn-secondary">
                        <i class="fas fa-store"></i> View Frontend
                    </a>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h3 class="section-title">
                <i class="fas fa-terminal"></i>
                System Log
            </h3>
            <div class="status-log" id="statusLog">
File protocol compatibility test initialized...
            </div>
        </div>
        
        <div class="test-section">
            <h3 class="section-title">
                <i class="fas fa-box"></i>
                Current Products
            </h3>
            <div class="results-grid" id="productsGrid">
                <!-- Products will be displayed here -->
            </div>
        </div>
    </div>

    <script>
        // File Protocol Product Manager (standalone)
        class FileProtocolProductManager {
            constructor() {
                this.products = this.loadProducts();
                this.log('FileProtocolProductManager initialized');
            }

            loadProducts() {
                try {
                    const products = localStorage.getItem('vaith_products');
                    return products ? JSON.parse(products) : [];
                } catch (error) {
                    this.log('Error loading products: ' + error.message);
                    return [];
                }
            }

            saveProducts() {
                try {
                    localStorage.setItem('vaith_products', JSON.stringify(this.products));
                    this.log('Products saved to localStorage');
                    return true;
                } catch (error) {
                    this.log('Error saving products: ' + error.message);
                    return false;
                }
            }

            addProduct(productData) {
                const newProduct = {
                    id: this.products.length > 0 ? Math.max(...this.products.map(p => p.id)) + 1 : 1,
                    ...productData,
                    brand: productData.brand || 'VAITH',
                    sku: productData.sku || 'FP-' + Date.now(),
                    status: productData.status || 'active',
                    createdDate: new Date().toISOString(),
                    updatedDate: new Date().toISOString(),
                    rating: 0,
                    reviews: 0,
                    images: productData.images || []
                };

                this.products.push(newProduct);
                this.saveProducts();
                this.log(`Product added: ${newProduct.name} (ID: ${newProduct.id})`);
                return newProduct;
            }

            getAllProducts() {
                return this.products;
            }

            clearProducts() {
                this.products = [];
                this.saveProducts();
                this.log('All products cleared');
            }

            log(message) {
                console.log('[FileProtocolProductManager]', message);
            }
        }

        // Initialize
        let productManager;
        
        document.addEventListener('DOMContentLoaded', function() {
            productManager = new FileProtocolProductManager();
            initializeTest();
            setupEventListeners();
        });

        function initializeTest() {
            log('🚀 File protocol test initialized');
            checkSystem();
            updateProductCount();
            displayProducts();
        }

        function setupEventListeners() {
            document.getElementById('quickAddForm').addEventListener('submit', function(e) {
                e.preventDefault();
                addQuickProduct();
            });
        }

        function log(message) {
            const logElement = document.getElementById('statusLog');
            const timestamp = new Date().toLocaleTimeString();
            logElement.textContent += `\n[${timestamp}] ${message}`;
            logElement.scrollTop = logElement.scrollHeight;
        }

        function checkSystem() {
            log('🔍 Checking system status...');
            
            // Check protocol
            const protocol = window.location.protocol;
            document.getElementById('currentProtocol').textContent = protocol;
            log(`Protocol: ${protocol}`);
            
            // Check localStorage
            const localStorageAvailable = typeof(Storage) !== "undefined";
            document.getElementById('localStorageStatus').textContent = localStorageAvailable ? 'Available' : 'Not Available';
            log(`localStorage: ${localStorageAvailable ? 'Available' : 'Not Available'}`);
            
            // Check products
            const productCount = productManager.getAllProducts().length;
            log(`Current products: ${productCount}`);
            
            if (protocol === 'file:') {
                log('✅ Running in file:// protocol - perfect for testing!');
            } else {
                log('ℹ️ Running in server environment');
            }
        }

        function addQuickProduct() {
            log('➕ Adding quick product...');
            
            const formData = new FormData(document.getElementById('quickAddForm'));
            const productData = {};
            
            for (let [key, value] of formData.entries()) {
                productData[key] = value;
            }
            
            // Convert numeric fields
            productData.price = parseFloat(productData.price);
            productData.stock = parseInt(productData.stock);
            
            try {
                const result = productManager.addProduct(productData);
                log(`✅ Product added successfully: ${result.name}`);
                updateProductCount();
                displayProducts();
                
                // Reset form
                document.getElementById('quickAddForm').reset();
                document.getElementById('quickName').value = 'File Protocol Test Product';
                document.getElementById('quickPrice').value = '39.99';
                document.getElementById('quickStock').value = '15';
                
            } catch (error) {
                log(`❌ Error adding product: ${error.message}`);
            }
        }

        function listProducts() {
            log('📋 Listing all products...');
            const products = productManager.getAllProducts();
            
            if (products.length === 0) {
                log('📭 No products found');
            } else {
                products.forEach((product, index) => {
                    log(`  ${index + 1}. ${product.name} - $${product.price} (${product.category})`);
                });
            }
        }

        function clearAllProducts() {
            if (confirm('Are you sure you want to clear all products?')) {
                log('🗑️ Clearing all products...');
                productManager.clearProducts();
                updateProductCount();
                displayProducts();
                log('✅ All products cleared');
            }
        }

        function updateProductCount() {
            const count = productManager.getAllProducts().length;
            document.getElementById('currentProductCount').textContent = count;
        }

        function displayProducts() {
            const products = productManager.getAllProducts();
            const grid = document.getElementById('productsGrid');
            
            if (products.length === 0) {
                grid.innerHTML = '<div class="result-card"><div class="result-title">No products found</div><div>Add a product to see it here</div></div>';
                return;
            }
            
            grid.innerHTML = products.map(product => `
                <div class="result-card">
                    <div class="result-title">${product.name}</div>
                    <div class="result-price">$${product.price}</div>
                    <div style="font-size: 0.875rem; color: var(--admin-text-secondary); margin-top: 0.5rem;">
                        ${product.category} • Stock: ${product.stock}
                    </div>
                </div>
            `).join('');
        }
    </script>
</body>
</html>
