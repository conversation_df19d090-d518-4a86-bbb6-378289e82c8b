@echo off
echo ========================================
echo VAITH File Protocol Compatibility Test
echo ========================================
echo.
echo This will open the VAITH admin system using file:// protocol
echo (no server required - works offline)
echo.
echo Opening test pages in your default browser...
echo.

REM Open the file protocol test page
start "" "file-protocol-test.html"

REM Wait a moment
timeout /t 2 /nobreak >nul

REM Open the admin products page
start "" "admin-products.html"

echo.
echo Pages opened! You can now:
echo 1. Test adding products in the admin panel
echo 2. View products on the frontend pages
echo 3. Use the file protocol test page for debugging
echo.
echo Press any key to also open the frontend pages...
pause >nul

REM Open frontend pages
start "" "products.html"
timeout /t 1 /nobreak >nul
start "" "index.html"

echo.
echo All pages opened successfully!
echo The system is now running in file:// protocol mode.
echo.
pause
